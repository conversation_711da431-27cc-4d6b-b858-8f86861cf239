import swaggerJSDoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import YAM<PERSON> from 'yamljs';
import path from 'path';
import { Express } from 'express';

// Load the OpenAPI specification from YAML file
const swaggerDocument = YAML.load(path.join(__dirname, '../../swagger.yaml'));

// Swagger UI options
const swaggerOptions = {
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Click2Cater API Documentation',
    customfavIcon: '/favicon.ico',
    swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true,
        docExpansion: 'none',
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        tryItOutEnabled: true
    }
};

/**
 * Setup Swagger documentation for the Express app
 * @param app Express application instance
 */
export const setupSwagger = (app: Express): void => {
    // Serve Swagger UI at /api-docs
    app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument, swaggerOptions));

    // Serve raw OpenAPI spec at /api-docs.json
    app.get('/api-docs.json', (req, res) => {
        res.setHeader('Content-Type', 'application/json');
        res.send(swaggerDocument);
    });

    // Serve raw OpenAPI spec at /api-docs.yaml
    app.get('/api-docs.yaml', (req, res) => {
        res.setHeader('Content-Type', 'text/yaml');
        res.send(YAML.stringify(swaggerDocument, 4));
    });

    console.log('📚 Swagger documentation available at:');
    console.log('   - Swagger UI: http://localhost:3000/api-docs');
    console.log('   - OpenAPI JSON: http://localhost:3000/api-docs.json');
    console.log('   - OpenAPI YAML: http://localhost:3000/api-docs.yaml');
};

export default swaggerDocument;
