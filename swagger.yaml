openapi: 3.0.3
info:
  title: Click2Cater Service API
  description: E-carting API for invoice management and user authentication
  version: 1.0.0
  contact:
    name: <PERSON><PERSON>
    email: <EMAIL>
  license:
    name: ISC
servers:
  - url: http://localhost:3000/api
    description: Development server
  - url: https://api.click2cater.com/api
    description: Production server

paths:
  /login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user with email and password
      operationId: loginUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
            example:
              email: <EMAIL>
              password: Pass@1234
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/invoice:
    get:
      tags:
        - Invoices
      summary: List invoices
      description: Get paginated list of invoices
      operationId: listInvoices
      security:
        - bearerAuth: []
      parameters:
        - name: skip
          in: query
          description: Number of records to skip for pagination
          required: false
          schema:
            type: integer
            default: 0
            minimum: 0
        - name: limit
          in: query
          description: Maximum number of records to return
          required: false
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
      responses:
        '200':
          description: Invoices retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceListResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      tags:
        - Invoices
      summary: Create invoice
      description: Create a new invoice
      operationId: createInvoice
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateInvoiceRequest'
            example:
              client_name: John Doe
              amount: 1500.50
              status: PENDING
              due_date: "2024-12-31"
              invoice_file_name: invoice_123.pdf
      responses:
        '200':
          description: Invoice created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceResponse'
        '400':
          description: Invalid input data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/invoice/{id}:
    get:
      tags:
        - Invoices
      summary: Get invoice details
      description: Retrieve details of a specific invoice by ID
      operationId: getInvoiceDetails
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: Invoice ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Invoice details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Invoice not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /invoice/{filename}:
    get:
      tags:
        - Media
      summary: Download invoice file
      description: Download invoice PDF file
      operationId: downloadInvoiceFile
      parameters:
        - name: filename
          in: path
          description: Invoice file name
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Invoice file
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        '400':
          description: File not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication

  schemas:
    LoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
          description: User email address
          example: <EMAIL>
        password:
          type: string
          description: User password
          example: Pass@1234

    LoginResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: User loggedIn successfully
        data:
          type: object
          properties:
            id:
              type: string
              format: uuid
              example: 123e4567-e89b-12d3-a456-************
            first_name:
              type: string
              example: Admin
            last_name:
              type: string
              example: Fintech
            email:
              type: string
              format: email
              example: <EMAIL>
            is_active:
              type: boolean
              example: true
            token:
              type: string
              description: JWT authentication token
              example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
            createdAt:
              type: string
              format: date-time
              example: "2023-01-01T00:00:00.000Z"
            updatedAt:
              type: string
              format: date-time
              example: "2023-01-01T00:00:00.000Z"

    CreateInvoiceRequest:
      type: object
      required:
        - client_name
        - amount
        - status
        - due_date
        - invoice_file_name
      properties:
        client_name:
          type: string
          description: Name of the client
          example: John Doe
        amount:
          type: number
          format: decimal
          description: Invoice amount
          example: 1500.50
        status:
          type: string
          enum: [PENDING, PAID, OVERDUE]
          description: Invoice status
          example: PENDING
        due_date:
          type: string
          format: date
          description: Invoice due date
          example: "2024-12-31"
        invoice_file_name:
          type: string
          description: Name of the invoice file
          example: invoice_123.pdf

    Invoice:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique invoice identifier
          example: 123e4567-e89b-12d3-a456-************
        invoice_number:
          type: string
          description: Auto-generated invoice number
          example: INV-1640995200000
        client_name:
          type: string
          description: Name of the client
          example: John Doe
        amount:
          type: number
          format: decimal
          description: Invoice amount
          example: 1500.50
        status:
          type: string
          enum: [PENDING, PAID, OVERDUE]
          description: Invoice status
          example: PENDING
        due_date:
          type: string
          format: date-time
          description: Invoice due date
          example: "2024-12-31T00:00:00.000Z"
        invoice_file_name:
          type: string
          description: Name of the invoice file
          example: invoice_123.pdf
        invoice_file_url:
          type: string
          format: uri
          description: URL to download the invoice file
          example: http://localhost:3000/invoice/invoice_123.pdf
        is_active:
          type: boolean
          description: Whether the invoice is active
          example: true
        createdAt:
          type: string
          format: date-time
          example: "2023-01-01T00:00:00.000Z"
        updatedAt:
          type: string
          format: date-time
          example: "2023-01-01T00:00:00.000Z"

    InvoiceResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: Invoice created successfully
        data:
          $ref: '#/components/schemas/Invoice'

    InvoiceListResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: Invoices fetched successfully
        data:
          type: array
          items:
            $ref: '#/components/schemas/Invoice'

    ErrorResponse:
      type: object
      properties:
        status:
          type: integer
          description: HTTP status code
          example: 400
        message:
          type: string
          description: Error message
          example: Invalid input data
        error:
          type: object
          description: Detailed error information
          example: {}

tags:
  - name: Authentication
    description: User authentication endpoints
  - name: Invoices
    description: Invoice management endpoints
  - name: Media
    description: File download endpoints
