import { NextFunction, Request, Response } from 'express';
import models from '../models';
import { httpStatusCodes } from '../utils/constants';
import { logger } from '../utils/logger';
import dotenv from 'dotenv';
dotenv.config();

class Invoice {
    /**
     * @swagger
     * /auth/invoice:
     *   post:
     *     tags:
     *       - Invoices
     *     summary: Create a new invoice
     *     description: Create a new invoice with client details and file information
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/CreateInvoiceRequest'
     *           example:
     *             client_name: <PERSON>
     *             amount: 1500.50
     *             status: PENDING
     *             due_date: "2024-12-31"
     *             invoice_file_name: invoice_123.pdf
     *     responses:
     *       200:
     *         description: Invoice created successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 status:
     *                   type: integer
     *                   example: 200
     *                 message:
     *                   type: string
     *                   example: Invoice created successfully
     *                 data:
     *                   $ref: '#/components/schemas/Invoice'
     *       400:
     *         description: Invalid input data
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ErrorResponse'
     *       401:
     *         description: Unauthorized
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ErrorResponse'
     *       500:
     *         description: Internal server error
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ErrorResponse'
     */
    async createInvoice(req: Request, res: Response, next: NextFunction) {
        try {
            const { client_name, amount, status, due_date, invoice_file_name } = req.body;

            /// auto generate invoice number
            const invoice_number = `INV-${Date.now()}`;

            /// generate file url
            const invoice_file_url = `${process.env.BASE_URL}/invoice/${invoice_file_name}`;

            const invoice = await models.invoices.create({
                invoice_number,
                client_name,
                amount,
                status,
                due_date,
                invoice_file_name,
                invoice_file_url
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Invoice created successfully`,
                data: invoice
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @swagger
     * /auth/invoice:
     *   get:
     *     tags:
     *       - Invoices
     *     summary: List invoices
     *     description: Get paginated list of invoices
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - name: skip
     *         in: query
     *         description: Number of records to skip for pagination
     *         required: false
     *         schema:
     *           type: integer
     *           default: 0
     *           minimum: 0
     *       - name: limit
     *         in: query
     *         description: Maximum number of records to return
     *         required: false
     *         schema:
     *           type: integer
     *           default: 10
     *           minimum: 1
     *           maximum: 100
     *     responses:
     *       200:
     *         description: Invoices retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 status:
     *                   type: integer
     *                   example: 200
     *                 message:
     *                   type: string
     *                   example: Invoices fetched successfully
     *                 data:
     *                   type: array
     *                   items:
     *                     $ref: '#/components/schemas/Invoice'
     *       401:
     *         description: Unauthorized
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ErrorResponse'
     *       500:
     *         description: Internal server error
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ErrorResponse'
     */
    async listInvoice(req: Request, res: Response, next: NextFunction) {
        try {
            // skip limit
            const { skip = 0, limit = 10 } = req.query;

            const invoices = await models.invoices.findAll({
                offset: Number(skip),
                limit: Number(limit)
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Invoices fetched successfully`,
                data: invoices
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @swagger
     * /auth/invoice/{id}:
     *   get:
     *     tags:
     *       - Invoices
     *     summary: Get invoice details
     *     description: Retrieve details of a specific invoice by ID
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - name: id
     *         in: path
     *         description: Invoice ID
     *         required: true
     *         schema:
     *           type: string
     *           format: uuid
     *     responses:
     *       200:
     *         description: Invoice details retrieved successfully
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 status:
     *                   type: integer
     *                   example: 200
     *                 message:
     *                   type: string
     *                   example: Invoice fetched successfully
     *                 data:
     *                   $ref: '#/components/schemas/Invoice'
     *       401:
     *         description: Unauthorized
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ErrorResponse'
     *       404:
     *         description: Invoice not found
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ErrorResponse'
     *       500:
     *         description: Internal server error
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ErrorResponse'
     */
    async invoiceDetails(req: Request, res: Response, next: NextFunction) {
        try {
            const { id } = req.params;

            const invoice = await models.invoices.findOne({
                where: { id }
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Invoice fetched successfully`,
                data: invoice
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new Invoice();
