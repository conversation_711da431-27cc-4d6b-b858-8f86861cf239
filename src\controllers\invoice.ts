

import { NextFunction, Request, Response } from 'express';
import models from '../models';
import { httpStatusCodes } from '../utils/constants';
import { logger } from '../utils/logger';
import dotenv from 'dotenv';
dotenv.config();

class Invoice {


    /**
     * @api {post} /v1/invoice/create
     * @apiName createInvoice
     * @apiGroup Invoice
     *
     *
     * @apiSuccess {Object} Invoice.
     */
    async createInvoice(req: Request, res: Response, next: NextFunction) {
        try {
            const { client_name, amount, status, due_date, invoice_file_name } = req.body;

            /// auto generate invoice number
            const invoice_number = `INV-${Date.now()}`;

            /// generate file url
            const invoice_file_url = `${process.env.BASE_URL}/invoice/${invoice_file_name}`;

            const invoice = await models.invoices.create({
                invoice_number,
                client_name,
                amount,
                status,
                due_date,
                invoice_file_name,
                invoice_file_url
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Invoice created successfully`,
                data: invoice
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }


    /**
     * @api {get} /v1/invoice/list
     * @apiName listInvoice
     * @apiGroup Invoice
     *
     *
     * @apiSuccess {Object} Invoices.
     */
    async listInvoice(req: Request, res: Response, next: NextFunction) {
        try {

            // skip limit
            const { skip = 0, limit = 10 } = req.query;

            const invoices = await models.invoices.findAll({
                offset: Number(skip),
                limit: Number(limit)
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Invoices fetched successfully`,
                data: invoices
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

    /**
     * @api {get} /v1/invoice/:id
     * @apiName invoiceDetails
     * @apiGroup Invoice
     *
     *
     * @apiSuccess {Object} Invoice.
     */
    async invoiceDetails(req: Request, res: Response, next: NextFunction) {
        try {
            const { id } = req.params;

            const invoice = await models.invoices.findOne({
                where: { id }
            });

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `Invoice fetched successfully`,
                data: invoice
            });
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }


}

export default new Invoice();
