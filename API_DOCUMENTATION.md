# API Documentation Guide

This guide explains how to view, update, and manage the API documentation for the Click2Cater Service.

## 📚 Overview

The API documentation has been migrated from **apidoc** to **OpenAPI 3.0 (Swagger)** with **SwaggerHub** integration for better collaboration and hosting.

### What Changed:
- ❌ Removed: apidoc dependency and configuration
- ✅ Added: OpenAPI 3.0 specification (`swagger.yaml`)
- ✅ Added: Swagger UI for local development
- ✅ Added: SwaggerHub integration for cloud hosting
- ✅ Added: Automated documentation scripts

## 🚀 How to View API Documentation

### 1. Local Development (Swagger UI)

Start your development server and visit the Swagger UI:

```bash
npm run dev
```

Then open your browser and navigate to:
- **Swagger UI**: http://localhost:3000/api-docs
- **OpenAPI JSON**: http://localhost:3000/api-docs.json  
- **OpenAPI YAML**: http://localhost:3000/api-docs.yaml

### 2. SwaggerHub (Cloud Hosting)

After publishing to SwaggerHub, your documentation will be available at:
- **SwaggerHub URL**: https://app.swaggerhub.com/apis/[your-username]/click2cater-service/1.0.0

## 🔧 Managing Documentation

### Initial Setup

1. **Create SwaggerHub Account**:
   - Go to https://app.swaggerhub.com/
   - Sign up for a free account
   - Note your username for configuration

2. **Configure SwaggerHub CLI**:
   ```bash
   swaggerhub configure
   ```
   Enter your SwaggerHub API key when prompted.

3. **Update Configuration**:
   Edit `swaggerhub.config.json` and replace `your-swaggerhub-username` with your actual SwaggerHub username.

### Publishing to SwaggerHub

#### First Time Publishing:
```bash
npm run docs:publish
```

#### Updating Existing Documentation:
```bash
npm run docs:update
```

### Available Scripts

| Script | Description |
|--------|-------------|
| `npm run docs:validate` | Validate the OpenAPI specification |
| `npm run docs:publish` | Publish API to SwaggerHub (first time) |
| `npm run docs:update` | Update existing API on SwaggerHub |
| `npm run docs:serve` | Serve documentation locally (alternative) |

## 📝 Updating API Documentation

### 1. Edit the OpenAPI Specification

The main documentation file is `swagger.yaml`. Update this file when:
- Adding new endpoints
- Modifying request/response schemas
- Changing authentication methods
- Updating API information

### 2. Key Sections to Update:

#### Adding New Endpoints:
```yaml
paths:
  /api/new-endpoint:
    post:
      tags:
        - YourTag
      summary: Brief description
      description: Detailed description
      operationId: uniqueOperationId
      # ... rest of endpoint definition
```

#### Adding New Schemas:
```yaml
components:
  schemas:
    NewModel:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
      required:
        - name
```

### 3. Validation and Testing:

Always validate your changes:
```bash
npm run docs:validate
```

Test locally:
```bash
npm run dev
# Visit http://localhost:3000/api-docs
```

### 4. Publishing Updates:

After making changes:
```bash
npm run docs:update
```

## 🔐 Authentication in Documentation

The API uses JWT Bearer token authentication. In Swagger UI:

1. Click the **"Authorize"** button
2. Enter: `Bearer YOUR_JWT_TOKEN`
3. Click **"Authorize"**
4. Now you can test authenticated endpoints

To get a JWT token:
1. Use the `/api/login` endpoint with valid credentials
2. Copy the token from the response
3. Use it in the authorization header

## 🌐 SwaggerHub Features

### Collaboration:
- Share documentation with team members
- Comment on specific endpoints
- Track changes and versions

### Auto-Generated Code:
- Client SDKs in multiple languages
- Server stubs
- Mock servers for testing

### Integration:
- GitHub integration for automatic updates
- CI/CD pipeline integration
- API monitoring and analytics

## 📋 Best Practices

### 1. Documentation Standards:
- Use clear, descriptive summaries
- Provide detailed descriptions for complex endpoints
- Include realistic examples
- Document all possible response codes

### 2. Schema Definitions:
- Define reusable schemas in `components/schemas`
- Use proper data types and formats
- Include validation rules (min, max, pattern)
- Add meaningful descriptions

### 3. Security:
- Document authentication requirements
- Specify required scopes/permissions
- Include security considerations

### 4. Versioning:
- Update version numbers when making breaking changes
- Maintain backward compatibility when possible
- Document migration guides for major versions

## 🚨 Troubleshooting

### Common Issues:

1. **SwaggerHub CLI not found**:
   ```bash
   npm install -g swaggerhub-cli
   ```

2. **Authentication errors**:
   - Verify your SwaggerHub API key
   - Run `swaggerhub configure` again

3. **Validation errors**:
   - Check YAML syntax
   - Ensure all references are valid
   - Use `npm run docs:validate`

4. **Local Swagger UI not loading**:
   - Ensure server is running
   - Check console for errors
   - Verify `swagger.yaml` exists

### Getting Help:

- **SwaggerHub Documentation**: https://support.smartbear.com/swaggerhub/docs/
- **OpenAPI Specification**: https://swagger.io/specification/
- **Swagger UI Documentation**: https://swagger.io/tools/swagger-ui/

## 📞 Support

For questions about the API documentation setup, contact the development team or create an issue in the project repository.

---

**Happy Documenting! 📚✨**
