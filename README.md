# Click2Cater Service API

E-carting API for invoice management and user authentication.

## 📚 API Documentation

The API documentation is now available through **OpenAPI 3.0 (Swagger)** with **SwaggerHub** integration.

### Quick Access:
- **Local Swagger UI**: http://localhost:3000/api-docs (when server is running)
- **SwaggerHub**: https://app.swaggerhub.com/apis/[your-username]/click2cater-service/1.0.0

### Documentation Commands:
```bash
# View documentation locally
npm run dev
# Then visit: http://localhost:3000/api-docs

# Validate OpenAPI spec
npm run docs:validate

# Publish to SwaggerHub (first time)
npm run docs:publish

# Update SwaggerHub documentation
npm run docs:update
```

For detailed documentation setup and management instructions, see [API_DOCUMENTATION.md](./API_DOCUMENTATION.md).

## 🚀 Getting Started

### Prerequisites
- Node.js (v14 or higher)
- PostgreSQL database
- npm or yarn

### Installation
```bash
# Install dependencies
npm install

# Build the project
npm run build

# Start development server
npm run dev
```

### Environment Variables
Create a `.env` file with the required environment variables (see `.env.example` if available).

## 📖 API Endpoints

### Authentication
- `POST /api/login` - User login

### Invoices (Authenticated)
- `GET /api/auth/invoice` - List invoices with pagination
- `POST /api/auth/invoice` - Create new invoice
- `GET /api/auth/invoice/:id` - Get invoice details

### Media
- `GET /invoice/:filename` - Download invoice file

## 🛠️ Development

```bash
# Development with hot reload
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run linting
npm run lint

# Format code
npm run prettier-all
```

## 📝 License

ISC License