import { NextFunction, Request, Response } from 'express';
import models from '../models';
import { TOKEN, httpStatusCodes } from '../utils/constants';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';

class User {
    /**
     * User login authentication
     */
    async loginUser(req: Request, res: Response, next: NextFunction) {
        try {
            const { email, password } = req.body;

            const user = await models.users.findOne({
                where: { email }
            });

            if (!user) {
                throw new Error('User not found');
            }

            if (user.is_blocked || !user.is_active) {
                throw new Error(`User is blocked, please contact administrator.!!!`);
            }

            const isPasswordMatched = await bcrypt.compare(password, user.password);

            if (!isPasswordMatched) {
                throw new Error('Incorrect password');
            }

            const authToken = await jwt.sign({ id: user.id, role: 'user' }, TOKEN);

            /// remove password from response
            const { password: _, ...rest } = JSON.parse(JSON.stringify(user));

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `User loggedIn successfully`,
                data: { ...rest, token: authToken }
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new User();
