import { NextFunction, Request, Response } from 'express';
import models from '../models';
import { TOKEN, httpStatusCodes } from '../utils/constants';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';

class User {
    /**
     * @swagger
     * /login:
     *   post:
     *     tags:
     *       - Authentication
     *     summary: User login
     *     description: Authenticate user with email and password
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/LoginRequest'
     *           example:
     *             email: <EMAIL>
     *             password: Pass@1234
     *     responses:
     *       200:
     *         description: Login successful
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 status:
     *                   type: integer
     *                   example: 200
     *                 message:
     *                   type: string
     *                   example: User loggedIn successfully
     *                 data:
     *                   type: object
     *                   properties:
     *                     id:
     *                       type: string
     *                       format: uuid
     *                       example: 123e4567-e89b-12d3-a456-************
     *                     first_name:
     *                       type: string
     *                       example: Admin
     *                     last_name:
     *                       type: string
     *                       example: Fintech
     *                     email:
     *                       type: string
     *                       format: email
     *                       example: <EMAIL>
     *                     is_active:
     *                       type: boolean
     *                       example: true
     *                     token:
     *                       type: string
     *                       description: JWT authentication token
     *                       example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
     *                     createdAt:
     *                       type: string
     *                       format: date-time
     *                       example: "2023-01-01T00:00:00.000Z"
     *                     updatedAt:
     *                       type: string
     *                       format: date-time
     *                       example: "2023-01-01T00:00:00.000Z"
     *       400:
     *         description: Invalid credentials or user blocked
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ErrorResponse'
     *             examples:
     *               userNotFound:
     *                 summary: User not found
     *                 value:
     *                   status: 400
     *                   message: User not found
     *                   error: {}
     *               userBlocked:
     *                 summary: User blocked
     *                 value:
     *                   status: 400
     *                   message: User is blocked, please contact administrator.!!!
     *                   error: {}
     *               incorrectPassword:
     *                 summary: Incorrect password
     *                 value:
     *                   status: 400
     *                   message: Incorrect password
     *                   error: {}
     *       500:
     *         description: Internal server error
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ErrorResponse'
     */
    async loginUser(req: Request, res: Response, next: NextFunction) {
        try {
            const { email, password } = req.body;

            const user = await models.users.findOne({
                where: { email }
            });

            if (!user) {
                throw new Error('User not found');
            }

            if (user.is_blocked || !user.is_active) {
                throw new Error(`User is blocked, please contact administrator.!!!`);
            }

            const isPasswordMatched = await bcrypt.compare(password, user.password);

            if (!isPasswordMatched) {
                throw new Error('Incorrect password');
            }

            const authToken = await jwt.sign({ id: user.id, role: 'user' }, TOKEN);

            /// remove password from response
            const { password: _, ...rest } = JSON.parse(JSON.stringify(user));

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `User loggedIn successfully`,
                data: { ...rest, token: authToken }
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new User();
